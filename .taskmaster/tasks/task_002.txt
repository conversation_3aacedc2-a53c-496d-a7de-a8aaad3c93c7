# Task ID: 2
# Title: Configure Local Supabase Instance
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Set up a local Supabase instance with test data for development and testing.
# Details:
Install Supabase CLI and initialize a new project. Populate with test data for all schemas. Use `supabase status` to get DB URL. Recommended: Supabase CLI v1+.

# Test Strategy:
Verify instance is running and accessible. Check test data is present in all schemas.
